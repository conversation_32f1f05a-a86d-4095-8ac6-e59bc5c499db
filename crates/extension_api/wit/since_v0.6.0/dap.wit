interface dap {
    use common.{env-vars};

    /// Resolves a specified TcpArgumentsTemplate into TcpArguments
    resolve-tcp-template: func(template: tcp-arguments-template) -> result<tcp-arguments, string>;

    record launch-request {
        program: string,
        cwd: option<string>,
        args: list<string>,
        envs: env-vars,
    }

    record attach-request {
        process-id: option<u32>,
    }

    variant debug-request {
        launch(launch-request),
        attach(attach-request)
    }

    record tcp-arguments {
        port: u16,
        host: u32,
        timeout: option<u64>,
    }

    record tcp-arguments-template {
        port: option<u16>,
        host: option<u32>,
        timeout: option<u64>,
    }

    record debug-task-definition {
        label: string,
        adapter: string,
        config: string,
        tcp-connection: option<tcp-arguments-template>,
    }

    enum start-debugging-request-arguments-request {
        launch,
        attach,
    }

    record start-debugging-request-arguments {
        configuration: string,
        request: start-debugging-request-arguments-request,
    }

    record debug-adapter-binary {
        command: option<string>,
        arguments: list<string>,
        envs: env-vars,
        cwd: option<string>,
        connection: option<tcp-arguments>,
        request-args: start-debugging-request-arguments
    }
}
